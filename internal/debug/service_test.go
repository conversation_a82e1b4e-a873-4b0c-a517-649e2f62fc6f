package debug

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/apidoc"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestLogger 测试用的简单日志记录器
type TestLogger struct {
	logs []string
}

func (l *TestLogger) Info(msg string, args ...interface{}) {
	l.logs = append(l.logs, "INFO: "+msg)
}

func (l *TestLogger) Error(msg string, args ...interface{}) {
	l.logs = append(l.logs, "ERROR: "+msg)
}

func (l *TestLogger) Debug(msg string, args ...interface{}) {
	l.logs = append(l.logs, "DEBUG: "+msg)
}

func (l *TestLogger) Warn(msg string, args ...interface{}) {
	l.logs = append(l.logs, "WARN: "+msg)
}

func (l *TestLogger) Fatal(msg string, args ...interface{}) {
	l.logs = append(l.logs, "FATAL: "+msg)
}

// TestNewService 测试创建调试服务
func TestNewService(t *testing.T) {
	// 创建真实的API文档服务
	apiDocConfig := apidoc.DefaultServiceConfig()
	apiDocLogger := apidoc.NewSimpleLogger()
	apiDocService := apidoc.NewService(apiDocConfig, apiDocLogger)

	testLogger := &TestLogger{}

	config := DefaultServiceConfig()
	service := NewService(apiDocService, config, testLogger)

	assert.NotNil(t, service, "服务不应为空")
	assert.NotNil(t, service.httpClient, "HTTP客户端不应为空")
	assert.NotNil(t, service.history, "历史记录不应为空")
	assert.Equal(t, config.Timeout, service.httpClient.Timeout, "超时时间应该匹配")
}

// TestSendRequest 测试发送请求
func TestSendRequest(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求
		assert.Equal(t, "POST", r.Method, "请求方法应该是POST")
		assert.Equal(t, "/api/test", r.URL.Path, "请求路径应该匹配")
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"), "Content-Type应该是JSON")
		
		// 读取请求体
		var body map[string]interface{}
		err := json.NewDecoder(r.Body).Decode(&body)
		require.NoError(t, err, "解析请求体失败")
		assert.Equal(t, "test", body["key"], "请求体应该匹配")
		
		// 返回响应
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "测试成功",
		})
	}))
	defer server.Close()
	
	// 创建服务
	apiDocConfig := apidoc.DefaultServiceConfig()
	apiDocLogger := apidoc.NewSimpleLogger()
	apiDocService := apidoc.NewService(apiDocConfig, apiDocLogger)

	testLogger := &TestLogger{}

	config := ServiceConfig{
		BaseURL:        server.URL,
		Timeout:        10 * time.Second,
		MaxHistorySize: 100,
		DefaultHeaders: map[string]string{
			"User-Agent": "Test-Client",
		},
	}

	service := NewService(apiDocService, config, testLogger)
	
	// 创建调试请求
	req := &DebugRequest{
		Method: "POST",
		URL:    "/api/test",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: map[string]interface{}{
			"key": "test",
		},
		Tags:          []string{"测试"},
		Description:   "测试请求",
		SaveToHistory: true,
	}
	
	// 发送请求
	ctx := context.Background()
	response, err := service.SendRequest(ctx, req)
	
	// 验证结果
	require.NoError(t, err, "发送请求不应该失败")
	assert.NotNil(t, response, "响应不应为空")
	assert.True(t, response.Success, "请求应该成功")
	assert.NotEmpty(t, response.RequestID, "请求ID不应为空")
	assert.NotNil(t, response.Request, "请求记录不应为空")
	
	// 验证请求记录
	record := response.Request
	assert.Equal(t, "POST", record.Method, "方法应该匹配")
	assert.Equal(t, server.URL+"/api/test", record.URL, "URL应该匹配")
	assert.NotNil(t, record.Response, "响应记录不应为空")
	assert.Equal(t, http.StatusOK, record.Response.StatusCode, "状态码应该是200")
	assert.Greater(t, record.Duration, time.Duration(0), "耗时应该大于0")
	
	// 验证历史记录
	history, total, err := service.GetHistory(ctx, 10, 0)
	require.NoError(t, err, "获取历史记录不应该失败")
	assert.Equal(t, 1, total, "历史记录总数应该是1")
	assert.Len(t, history, 1, "历史记录列表长度应该是1")
	assert.Equal(t, record.ID, history[0].ID, "历史记录ID应该匹配")
}

// TestSendRequestWithError 测试发送请求失败的情况
func TestSendRequestWithError(t *testing.T) {
	apiDocConfig := apidoc.DefaultServiceConfig()
	apiDocLogger := apidoc.NewSimpleLogger()
	apiDocService := apidoc.NewService(apiDocConfig, apiDocLogger)

	testLogger := &TestLogger{}

	config := ServiceConfig{
		BaseURL:        "http://invalid-url-that-does-not-exist.com",
		Timeout:        1 * time.Second,
		MaxHistorySize: 100,
		DefaultHeaders: map[string]string{},
	}

	service := NewService(apiDocService, config, testLogger)
	
	req := &DebugRequest{
		Method:        "GET",
		URL:           "/api/test",
		SaveToHistory: true,
	}
	
	ctx := context.Background()
	response, err := service.SendRequest(ctx, req)
	
	// 请求应该返回响应但标记为失败
	require.NoError(t, err, "SendRequest不应该返回错误")
	assert.NotNil(t, response, "响应不应为空")
	assert.False(t, response.Success, "请求应该标记为失败")
	assert.NotEmpty(t, response.Request.Error, "应该有错误信息")
}

// TestRequestHistory 测试请求历史功能
func TestRequestHistory(t *testing.T) {
	history := &RequestHistory{
		records: make([]RequestRecord, 0),
		maxSize: 5,
	}
	
	// 测试添加记录
	for i := 0; i < 3; i++ {
		record := RequestRecord{
			ID:        generateRequestID(),
			Timestamp: time.Now(),
			Method:    "GET",
			URL:       "/api/test",
		}
		history.Add(record)
	}
	
	// 验证记录数量
	assert.Equal(t, 3, history.Count(), "记录数量应该是3")
	
	// 测试获取记录
	records := history.Get(2, 0)
	assert.Len(t, records, 2, "应该返回2条记录")
	
	records = history.Get(2, 1)
	assert.Len(t, records, 2, "应该返回2条记录")
	
	records = history.Get(10, 0)
	assert.Len(t, records, 3, "应该返回所有3条记录")
	
	// 测试超出范围
	records = history.Get(2, 10)
	assert.Empty(t, records, "超出范围应该返回空列表")
	
	// 测试最大大小限制
	for i := 0; i < 5; i++ {
		record := RequestRecord{
			ID:        generateRequestID(),
			Timestamp: time.Now(),
			Method:    "POST",
			URL:       "/api/test",
		}
		history.Add(record)
	}
	
	assert.Equal(t, 5, history.Count(), "记录数量应该限制在最大值")
	
	// 测试根据ID获取记录
	firstRecord := history.Get(1, 0)[0]
	foundRecord := history.GetByID(firstRecord.ID)
	assert.NotNil(t, foundRecord, "应该找到记录")
	assert.Equal(t, firstRecord.ID, foundRecord.ID, "ID应该匹配")
	
	// 测试清空记录
	history.Clear()
	assert.Equal(t, 0, history.Count(), "清空后记录数量应该是0")
}

// TestGetEndpointTemplate 测试获取端点模板
func TestGetEndpointTemplate(t *testing.T) {
	// 跳过这个测试，因为它需要复杂的模拟设置
	t.Skip("跳过端点模板测试，需要完整的API文档服务")
}


// TestBuildFullURL 测试构建完整URL
func TestBuildFullURL(t *testing.T) {
	config := ServiceConfig{
		BaseURL: "http://localhost:8080",
	}
	service := &Service{config: config}
	
	testCases := []struct {
		input    string
		expected string
		desc     string
	}{
		{"/api/users", "http://localhost:8080/api/users", "相对路径应该拼接基础URL"},
		{"api/users", "http://localhost:8080/api/users", "无前导斜杠的路径应该正确处理"},
		{"http://example.com/api", "http://example.com/api", "绝对URL应该保持不变"},
		{"https://example.com/api", "https://example.com/api", "HTTPS URL应该保持不变"},
	}
	
	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			result := service.buildFullURL(tc.input)
			assert.Equal(t, tc.expected, result, tc.desc)
		})
	}
}

// TestDefaultServiceConfig 测试默认配置
func TestDefaultServiceConfig(t *testing.T) {
	config := DefaultServiceConfig()
	
	assert.Equal(t, "http://localhost:8080", config.BaseURL, "默认基础URL应该正确")
	assert.Equal(t, 30*time.Second, config.Timeout, "默认超时时间应该正确")
	assert.Equal(t, 1000, config.MaxHistorySize, "默认最大历史记录数应该正确")
	assert.NotEmpty(t, config.DefaultHeaders, "默认头部不应为空")
	assert.Contains(t, config.DefaultHeaders, "User-Agent", "应该包含User-Agent头部")
}

// BenchmarkSendRequest 性能测试
func BenchmarkSendRequest(b *testing.B) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"success": true}`))
	}))
	defer server.Close()

	apiDocConfig := apidoc.DefaultServiceConfig()
	apiDocLogger := apidoc.NewSimpleLogger()
	apiDocService := apidoc.NewService(apiDocConfig, apiDocLogger)

	testLogger := &TestLogger{}

	config := ServiceConfig{
		BaseURL:        server.URL,
		Timeout:        10 * time.Second,
		MaxHistorySize: 1000,
		DefaultHeaders: map[string]string{},
	}

	service := NewService(apiDocService, config, testLogger)
	
	req := &DebugRequest{
		Method:        "GET",
		URL:           "/api/test",
		SaveToHistory: false, // 不保存历史以提高性能
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.SendRequest(ctx, req)
		if err != nil {
			b.Fatal(err)
		}
	}
}
