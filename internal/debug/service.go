package debug

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/apidoc"
	"ai-text-game-iam-npc/pkg/logger"
)

// Service API调试服务
type Service struct {
	apiDocService *apidoc.Service
	httpClient    *http.Client
	history       *RequestHistory
	logger        logger.Logger
	config        ServiceConfig
}

// ServiceConfig 调试服务配置
type ServiceConfig struct {
	BaseURL         string        // 基础URL
	Timeout         time.Duration // 请求超时时间
	MaxHistorySize  int           // 最大历史记录数
	DefaultHeaders  map[string]string // 默认请求头
}

// RequestHistory 请求历史记录
type RequestHistory struct {
	records []RequestRecord
	mu      sync.RWMutex
	maxSize int
}

// RequestRecord 请求记录
type RequestRecord struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Method      string                 `json:"method"`
	URL         string                 `json:"url"`
	Headers     map[string]string      `json:"headers"`
	Body        interface{}            `json:"body"`
	Response    *ResponseRecord        `json:"response"`
	Duration    time.Duration          `json:"duration"`
	Error       string                 `json:"error,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Description string                 `json:"description,omitempty"`
}

// ResponseRecord 响应记录
type ResponseRecord struct {
	StatusCode int                    `json:"status_code"`
	Headers    map[string][]string    `json:"headers"`
	Body       interface{}            `json:"body"`
	Size       int64                  `json:"size"`
}

// DebugRequest 调试请求
type DebugRequest struct {
	Method      string                 `json:"method" binding:"required"`
	URL         string                 `json:"url" binding:"required"`
	Headers     map[string]string      `json:"headers"`
	Body        interface{}            `json:"body"`
	Tags        []string               `json:"tags"`
	Description string                 `json:"description"`
	SaveToHistory bool                 `json:"save_to_history"`
}

// DebugResponse 调试响应
type DebugResponse struct {
	RequestID string          `json:"request_id"`
	Request   *RequestRecord  `json:"request"`
	Success   bool            `json:"success"`
	Message   string          `json:"message"`
}

// NewService 创建新的调试服务
func NewService(apiDocService *apidoc.Service, config ServiceConfig, logger logger.Logger) *Service {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxHistorySize == 0 {
		config.MaxHistorySize = 1000
	}
	
	return &Service{
		apiDocService: apiDocService,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		history: &RequestHistory{
			records: make([]RequestRecord, 0),
			maxSize: config.MaxHistorySize,
		},
		logger: logger,
		config: config,
	}
}

// SendRequest 发送调试请求
func (s *Service) SendRequest(ctx context.Context, req *DebugRequest) (*DebugResponse, error) {
	s.logger.Info("发送调试请求", "method", req.Method, "url", req.URL)
	
	// 生成请求ID
	requestID := generateRequestID()
	
	// 构建完整URL
	fullURL := s.buildFullURL(req.URL)
	
	// 创建HTTP请求
	httpReq, err := s.createHTTPRequest(ctx, req, fullURL)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	
	// 记录开始时间
	startTime := time.Now()
	
	// 发送请求
	resp, err := s.httpClient.Do(httpReq)
	duration := time.Since(startTime)
	
	// 创建请求记录
	record := RequestRecord{
		ID:          requestID,
		Timestamp:   startTime,
		Method:      req.Method,
		URL:         fullURL,
		Headers:     req.Headers,
		Body:        req.Body,
		Duration:    duration,
		Tags:        req.Tags,
		Description: req.Description,
	}
	
	if err != nil {
		record.Error = err.Error()
		s.logger.Error("发送请求失败", "error", err, "url", fullURL)
		
		// 保存到历史记录
		if req.SaveToHistory {
			s.history.Add(record)
		}
		
		return &DebugResponse{
			RequestID: requestID,
			Request:   &record,
			Success:   false,
			Message:   fmt.Sprintf("请求失败: %s", err.Error()),
		}, nil
	}
	defer resp.Body.Close()
	
	// 读取响应
	responseRecord, err := s.readResponse(resp)
	if err != nil {
		record.Error = fmt.Sprintf("读取响应失败: %s", err.Error())
		s.logger.Error("读取响应失败", "error", err)
	} else {
		record.Response = responseRecord
	}
	
	// 保存到历史记录
	if req.SaveToHistory {
		s.history.Add(record)
	}
	
	s.logger.Info("请求完成", "status_code", resp.StatusCode, "duration", duration)
	
	return &DebugResponse{
		RequestID: requestID,
		Request:   &record,
		Success:   err == nil,
		Message:   "请求发送成功",
	}, nil
}

// GetHistory 获取请求历史
func (s *Service) GetHistory(ctx context.Context, limit, offset int) ([]RequestRecord, int, error) {
	return s.history.Get(limit, offset), s.history.Count(), nil
}

// GetHistoryByID 根据ID获取历史记录
func (s *Service) GetHistoryByID(ctx context.Context, id string) (*RequestRecord, error) {
	record := s.history.GetByID(id)
	if record == nil {
		return nil, fmt.Errorf("未找到ID为 %s 的请求记录", id)
	}
	return record, nil
}

// ClearHistory 清空历史记录
func (s *Service) ClearHistory(ctx context.Context) error {
	s.history.Clear()
	s.logger.Info("已清空请求历史记录")
	return nil
}

// GetEndpointTemplate 获取端点模板
func (s *Service) GetEndpointTemplate(ctx context.Context, method, path string) (*EndpointTemplate, error) {
	endpoints, err := s.apiDocService.GetEndpoints(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取API端点失败: %w", err)
	}

	// 首先尝试精确匹配
	for _, endpoint := range endpoints {
		if endpoint.Method == method && endpoint.Path == path {
			return s.convertToTemplate(&endpoint), nil
		}
	}

	// 如果精确匹配失败，尝试智能匹配
	matchedEndpoint := s.findBestMatch(endpoints, method, path)
	if matchedEndpoint != nil {
		s.logger.Info("使用智能匹配找到端点", "requested", path, "matched", matchedEndpoint.Path)
		return s.convertToTemplate(matchedEndpoint), nil
	}

	// 生成建议的相似端点
	suggestions := s.findSimilarEndpoints(endpoints, method, path)
	if len(suggestions) > 0 {
		suggestionStr := ""
		for i, suggestion := range suggestions {
			if i > 0 {
				suggestionStr += ", "
			}
			suggestionStr += fmt.Sprintf("%s %s", suggestion.Method, suggestion.Path)
		}
		return nil, fmt.Errorf("未找到匹配的端点: %s %s。建议的相似端点: %s", method, path, suggestionStr)
	}

	return nil, fmt.Errorf("未找到匹配的端点: %s %s", method, path)
}

// EndpointTemplate 端点模板
type EndpointTemplate struct {
	Method      string                 `json:"method"`
	Path        string                 `json:"path"`
	Summary     string                 `json:"summary"`
	Description string                 `json:"description"`
	Parameters  []ParameterTemplate    `json:"parameters"`
	Headers     map[string]string      `json:"headers"`
	BodySchema  interface{}            `json:"body_schema"`
	Examples    map[string]interface{} `json:"examples"`
}

// ParameterTemplate 参数模板
type ParameterTemplate struct {
	Name        string      `json:"name"`
	In          string      `json:"in"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Description string      `json:"description"`
	Example     interface{} `json:"example"`
	Options     []string    `json:"options,omitempty"`
}

// 辅助方法

// buildFullURL 构建完整URL
func (s *Service) buildFullURL(url string) string {
	if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
		return url
	}
	
	baseURL := strings.TrimRight(s.config.BaseURL, "/")
	url = strings.TrimLeft(url, "/")
	
	return fmt.Sprintf("%s/%s", baseURL, url)
}

// createHTTPRequest 创建HTTP请求
func (s *Service) createHTTPRequest(ctx context.Context, req *DebugRequest, url string) (*http.Request, error) {
	var body io.Reader
	
	// 处理请求体
	if req.Body != nil {
		switch v := req.Body.(type) {
		case string:
			body = strings.NewReader(v)
		case []byte:
			body = bytes.NewReader(v)
		default:
			jsonData, err := json.Marshal(v)
			if err != nil {
				return nil, fmt.Errorf("序列化请求体失败: %w", err)
			}
			body = bytes.NewReader(jsonData)
		}
	}
	
	// 创建请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, url, body)
	if err != nil {
		return nil, err
	}
	
	// 设置默认头部
	for key, value := range s.config.DefaultHeaders {
		httpReq.Header.Set(key, value)
	}
	
	// 设置请求头
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}
	
	// 如果有请求体且没有设置Content-Type，设置为JSON
	if body != nil && httpReq.Header.Get("Content-Type") == "" {
		httpReq.Header.Set("Content-Type", "application/json")
	}
	
	return httpReq, nil
}

// readResponse 读取响应
func (s *Service) readResponse(resp *http.Response) (*ResponseRecord, error) {
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	
	// 尝试解析JSON
	var bodyData interface{}
	if len(bodyBytes) > 0 {
		if err := json.Unmarshal(bodyBytes, &bodyData); err != nil {
			// 如果不是JSON，保存为字符串
			bodyData = string(bodyBytes)
		}
	}
	
	return &ResponseRecord{
		StatusCode: resp.StatusCode,
		Headers:    resp.Header,
		Body:       bodyData,
		Size:       int64(len(bodyBytes)),
	}, nil
}

// convertToTemplate 转换为模板
func (s *Service) convertToTemplate(endpoint *apidoc.APIEndpoint) *EndpointTemplate {
	template := &EndpointTemplate{
		Method:      endpoint.Method,
		Path:        endpoint.Path,
		Summary:     endpoint.Summary,
		Description: endpoint.Description,
		Parameters:  make([]ParameterTemplate, 0),
		Headers:     make(map[string]string),
		Examples:    make(map[string]interface{}),
	}
	
	// 转换参数
	for _, param := range endpoint.Parameters {
		// 从Schema中提取类型信息
		paramType := param.Type // 向后兼容的内部类型
		if param.Schema != nil && param.Schema.Type != "" {
			paramType = param.Schema.Type
			// 如果有格式信息，添加到类型中
			if param.Schema.Format != "" {
				paramType = fmt.Sprintf("%s(%s)", param.Schema.Type, param.Schema.Format)
			}
		}

		// 使用Schema中的示例值，如果没有则使用参数的示例值
		example := param.Example
		if param.Schema != nil && param.Schema.Example != nil {
			example = param.Schema.Example
		}

		paramTemplate := ParameterTemplate{
			Name:        param.Name,
			In:          param.In,
			Type:        paramType,
			Required:    param.Required,
			Description: param.Description,
			Example:     example,
		}
		template.Parameters = append(template.Parameters, paramTemplate)
	}
	
	// 设置默认头部
	if endpoint.Method == "POST" || endpoint.Method == "PUT" || endpoint.Method == "PATCH" {
		template.Headers["Content-Type"] = "application/json"
	}
	
	return template
}

// findBestMatch 查找最佳匹配的端点
func (s *Service) findBestMatch(endpoints []apidoc.APIEndpoint, method, path string) *apidoc.APIEndpoint {
	// 智能匹配规则：
	// 1. 如果请求路径包含 /api/v1，尝试去掉这个前缀匹配
	// 2. 如果请求路径不包含 /api，尝试添加 /api 前缀匹配
	// 3. 支持路径参数的模糊匹配

	var candidates []*apidoc.APIEndpoint

	for i := range endpoints {
		endpoint := &endpoints[i]
		if endpoint.Method != method {
			continue
		}

		// 规则1: 去掉 /api/v1 前缀尝试匹配
		if strings.HasPrefix(path, "/api/v1/") {
			simplePath := strings.TrimPrefix(path, "/api/v1")
			if endpoint.Path == simplePath || endpoint.Path == "/api"+simplePath {
				candidates = append(candidates, endpoint)
			}
		}

		// 规则2: 添加 /api 前缀尝试匹配
		if !strings.HasPrefix(path, "/api/") {
			if endpoint.Path == "/api"+path {
				candidates = append(candidates, endpoint)
			}
		}

		// 规则3: 路径参数模糊匹配（简单实现）
		if s.pathsMatch(endpoint.Path, path) {
			candidates = append(candidates, endpoint)
		}
	}

	// 返回第一个候选者（可以进一步优化排序逻辑）
	if len(candidates) > 0 {
		return candidates[0]
	}

	return nil
}

// pathsMatch 检查两个路径是否匹配（考虑路径参数）
func (s *Service) pathsMatch(endpointPath, requestPath string) bool {
	// 简单的路径参数匹配实现
	// 将 :param 和 {param} 视为通配符
	endpointParts := strings.Split(endpointPath, "/")
	requestParts := strings.Split(requestPath, "/")

	if len(endpointParts) != len(requestParts) {
		return false
	}

	for i, endpointPart := range endpointParts {
		requestPart := requestParts[i]

		// 如果是路径参数，跳过比较
		if strings.HasPrefix(endpointPart, ":") ||
		   (strings.HasPrefix(endpointPart, "{") && strings.HasSuffix(endpointPart, "}")) {
			continue
		}

		// 精确匹配
		if endpointPart != requestPart {
			return false
		}
	}

	return true
}

// findSimilarEndpoints 查找相似的端点
func (s *Service) findSimilarEndpoints(endpoints []apidoc.APIEndpoint, method, path string) []apidoc.APIEndpoint {
	var similar []apidoc.APIEndpoint

	for _, endpoint := range endpoints {
		// 同方法的端点
		if endpoint.Method == method {
			// 计算路径相似度
			if s.calculatePathSimilarity(endpoint.Path, path) > 0.5 {
				similar = append(similar, endpoint)
			}
		}
	}

	// 限制建议数量
	if len(similar) > 3 {
		similar = similar[:3]
	}

	return similar
}

// calculatePathSimilarity 计算路径相似度
func (s *Service) calculatePathSimilarity(path1, path2 string) float64 {
	// 简单的相似度计算：基于公共子字符串
	if path1 == path2 {
		return 1.0
	}

	// 检查是否包含相同的关键词
	keywords1 := extractKeywords(path1)
	keywords2 := extractKeywords(path2)

	commonKeywords := 0
	for _, kw1 := range keywords1 {
		for _, kw2 := range keywords2 {
			if kw1 == kw2 {
				commonKeywords++
				break
			}
		}
	}

	totalKeywords := len(keywords1) + len(keywords2) - commonKeywords
	if totalKeywords == 0 {
		return 0.0
	}

	return float64(commonKeywords*2) / float64(totalKeywords)
}

// extractKeywords 从路径中提取关键词
func extractKeywords(path string) []string {
	// 移除路径参数和特殊字符，提取有意义的词汇
	path = strings.ToLower(path)
	parts := strings.Split(path, "/")

	var keywords []string
	for _, part := range parts {
		if part == "" || strings.HasPrefix(part, ":") ||
		   (strings.HasPrefix(part, "{") && strings.HasSuffix(part, "}")) {
			continue
		}

		// 过滤掉常见的API前缀
		if part != "api" && part != "v1" && part != "v2" {
			keywords = append(keywords, part)
		}
	}

	return keywords
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// RequestHistory 方法

// Add 添加记录
func (h *RequestHistory) Add(record RequestRecord) {
	h.mu.Lock()
	defer h.mu.Unlock()
	
	// 添加到开头
	h.records = append([]RequestRecord{record}, h.records...)
	
	// 限制大小
	if len(h.records) > h.maxSize {
		h.records = h.records[:h.maxSize]
	}
}

// Get 获取记录
func (h *RequestHistory) Get(limit, offset int) []RequestRecord {
	h.mu.RLock()
	defer h.mu.RUnlock()
	
	if offset >= len(h.records) {
		return []RequestRecord{}
	}
	
	end := offset + limit
	if end > len(h.records) {
		end = len(h.records)
	}
	
	return h.records[offset:end]
}

// GetByID 根据ID获取记录
func (h *RequestHistory) GetByID(id string) *RequestRecord {
	h.mu.RLock()
	defer h.mu.RUnlock()
	
	for _, record := range h.records {
		if record.ID == id {
			return &record
		}
	}
	
	return nil
}

// Count 获取记录数量
func (h *RequestHistory) Count() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	
	return len(h.records)
}

// Clear 清空记录
func (h *RequestHistory) Clear() {
	h.mu.Lock()
	defer h.mu.Unlock()
	
	h.records = h.records[:0]
}

// DefaultServiceConfig 默认服务配置
func DefaultServiceConfig() ServiceConfig {
	return ServiceConfig{
		BaseURL:        "http://localhost:8080",
		Timeout:        30 * time.Second,
		MaxHistorySize: 1000,
		DefaultHeaders: map[string]string{
			"User-Agent": "API-Debug-Tool/1.0",
		},
	}
}
