package test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/apidebug"
	"ai-text-game-iam-npc/internal/debug"
	"ai-text-game-iam-npc/internal/handlers"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAPIDebugSystemIntegration 测试API调试系统集成
func TestAPIDebugSystemIntegration(t *testing.T) {
	// 创建测试日志记录器
	testLogger := &TestLogger{}
	
	// 创建API调试服务
	config := apidebug.DefaultConfig()
	config.ProjectRoot = "../"  // 指向项目根目录
	config.BaseURL = "http://localhost:8080"
	
	service, err := apidebug.NewService(config, testLogger)
	require.NoError(t, err, "创建API调试服务失败")
	
	// 初始化服务
	ctx := context.Background()
	err = service.Initialize(ctx)
	require.NoError(t, err, "初始化服务失败")
	
	// 创建处理器
	apiDocHandler := handlers.NewAPIDocHandler(service.GetAPIDocService(), testLogger)
	debugHandler := handlers.NewDebugHandler(service.GetDebugService(), testLogger)
	systemHandler := handlers.NewAPIDebugHandler(service, testLogger)
	
	// 创建Gin路由
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	// 注册路由
	docsGroup := router.Group("/api/v1/docs")
	{
		docsGroup.GET("/openapi", apiDocHandler.GetOpenAPISpec)
		docsGroup.GET("/endpoints", apiDocHandler.GetEndpoints)
		docsGroup.GET("/stats", apiDocHandler.GetDocumentationStats)
		docsGroup.POST("/refresh", apiDocHandler.RefreshDocumentation)
	}
	
	debugGroup := router.Group("/api/v1/debug")
	{
		debugGroup.POST("/request", debugHandler.SendRequest)
		debugGroup.GET("/history", debugHandler.GetHistory)
		debugGroup.GET("/history/:id", debugHandler.GetHistoryByID)
		debugGroup.DELETE("/history", debugHandler.ClearHistory)
		debugGroup.GET("/template", debugHandler.GetEndpointTemplate)
	}
	
	systemGroup := router.Group("/api/v1/system")
	{
		systemGroup.GET("/status", systemHandler.GetSystemStatus)
		systemGroup.GET("/config", systemHandler.GetSystemConfig)
	}
	
	// 测试API文档功能
	t.Run("测试API文档功能", func(t *testing.T) {
		testAPIDocumentationFeatures(t, router)
	})
	
	// 测试API调试功能
	t.Run("测试API调试功能", func(t *testing.T) {
		testAPIDebugFeatures(t, router)
	})
	
	// 测试系统状态功能
	t.Run("测试系统状态功能", func(t *testing.T) {
		testSystemStatusFeatures(t, router)
	})
}

// testAPIDocumentationFeatures 测试API文档功能
func testAPIDocumentationFeatures(t *testing.T, router *gin.Engine) {
	// 测试获取OpenAPI规范
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/docs/openapi", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取OpenAPI规范应该成功")
	
	var openAPISpec map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &openAPISpec)
	require.NoError(t, err, "解析OpenAPI规范失败")
	
	assert.Equal(t, "3.0.3", openAPISpec["openapi"], "OpenAPI版本应该正确")
	assert.NotNil(t, openAPISpec["info"], "应该包含info字段")
	assert.NotNil(t, openAPISpec["paths"], "应该包含paths字段")
	
	// 测试获取API端点列表
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/v1/docs/endpoints", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取API端点列表应该成功")
	
	var endpointsResponse handlers.Response
	err = json.Unmarshal(w.Body.Bytes(), &endpointsResponse)
	require.NoError(t, err, "解析端点列表响应失败")
	
	assert.True(t, endpointsResponse.Success, "响应应该成功")
	assert.NotNil(t, endpointsResponse.Data, "应该包含数据")
	
	// 测试获取文档统计
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/v1/docs/stats", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取文档统计应该成功")
	
	var statsResponse handlers.Response
	err = json.Unmarshal(w.Body.Bytes(), &statsResponse)
	require.NoError(t, err, "解析统计响应失败")
	
	assert.True(t, statsResponse.Success, "响应应该成功")
	
	// 测试刷新文档
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("POST", "/api/v1/docs/refresh", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "刷新文档应该成功")
}

// testAPIDebugFeatures 测试API调试功能
func testAPIDebugFeatures(t *testing.T, router *gin.Engine) {
	// 创建测试服务器作为调试目标
	testServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "测试响应",
			"method":  r.Method,
			"path":    r.URL.Path,
		})
	}))
	defer testServer.Close()
	
	// 测试发送调试请求
	debugRequest := debug.DebugRequest{
		Method: "GET",
		URL:    testServer.URL + "/api/test",
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Description:   "集成测试请求",
		Tags:          []string{"测试"},
		SaveToHistory: true,
	}
	
	requestBody, _ := json.Marshal(debugRequest)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/api/v1/debug/request", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "发送调试请求应该成功")
	
	var debugResponse handlers.Response
	err := json.Unmarshal(w.Body.Bytes(), &debugResponse)
	require.NoError(t, err, "解析调试响应失败")
	
	assert.True(t, debugResponse.Success, "调试请求应该成功")
	assert.NotNil(t, debugResponse.Data, "应该包含响应数据")
	
	// 测试获取请求历史
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/v1/debug/history", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取请求历史应该成功")
	
	var historyResponse handlers.Response
	err = json.Unmarshal(w.Body.Bytes(), &historyResponse)
	require.NoError(t, err, "解析历史响应失败")
	
	assert.True(t, historyResponse.Success, "获取历史应该成功")
	
	// 验证历史记录包含我们刚才的请求
	historyData := historyResponse.Data.(map[string]interface{})
	records := historyData["records"].([]interface{})
	assert.NotEmpty(t, records, "历史记录不应为空")
	
	// 测试清空历史记录
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("DELETE", "/api/v1/debug/history", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "清空历史记录应该成功")
	
	// 验证历史记录已清空
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/v1/debug/history", nil)
	router.ServeHTTP(w, req)
	
	err = json.Unmarshal(w.Body.Bytes(), &historyResponse)
	require.NoError(t, err, "解析历史响应失败")
	
	historyData = historyResponse.Data.(map[string]interface{})
	records = historyData["records"].([]interface{})
	assert.Empty(t, records, "历史记录应该为空")
}

// testSystemStatusFeatures 测试系统状态功能
func testSystemStatusFeatures(t *testing.T, router *gin.Engine) {
	// 测试获取系统状态
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/api/v1/system/status", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取系统状态应该成功")
	
	var statusResponse handlers.Response
	err := json.Unmarshal(w.Body.Bytes(), &statusResponse)
	require.NoError(t, err, "解析状态响应失败")
	
	assert.True(t, statusResponse.Success, "获取状态应该成功")
	assert.NotNil(t, statusResponse.Data, "应该包含状态数据")
	
	// 验证状态数据结构
	statusData := statusResponse.Data.(map[string]interface{})
	assert.Contains(t, statusData, "service_name", "应该包含服务名称")
	assert.Contains(t, statusData, "version", "应该包含版本信息")
	assert.Contains(t, statusData, "status", "应该包含状态信息")
	assert.Contains(t, statusData, "documentation", "应该包含文档状态")
	assert.Contains(t, statusData, "debug", "应该包含调试状态")
	assert.Contains(t, statusData, "configuration", "应该包含配置信息")
	
	// 测试获取系统配置
	w = httptest.NewRecorder()
	req, _ = http.NewRequest("GET", "/api/v1/system/config", nil)
	router.ServeHTTP(w, req)
	
	assert.Equal(t, http.StatusOK, w.Code, "获取系统配置应该成功")
	
	var configResponse handlers.Response
	err = json.Unmarshal(w.Body.Bytes(), &configResponse)
	require.NoError(t, err, "解析配置响应失败")
	
	assert.True(t, configResponse.Success, "获取配置应该成功")
	assert.NotNil(t, configResponse.Data, "应该包含配置数据")
}

// TestLogger 测试用的日志记录器
type TestLogger struct {
	logs []LogEntry
}

type LogEntry struct {
	Level   string
	Message string
	Args    []interface{}
	Time    time.Time
}

func (l *TestLogger) Info(msg string, args ...interface{}) {
	l.logs = append(l.logs, LogEntry{
		Level:   "INFO",
		Message: msg,
		Args:    args,
		Time:    time.Now(),
	})
}

func (l *TestLogger) Error(msg string, args ...interface{}) {
	l.logs = append(l.logs, LogEntry{
		Level:   "ERROR",
		Message: msg,
		Args:    args,
		Time:    time.Now(),
	})
}

func (l *TestLogger) Debug(msg string, args ...interface{}) {
	l.logs = append(l.logs, LogEntry{
		Level:   "DEBUG",
		Message: msg,
		Args:    args,
		Time:    time.Now(),
	})
}

func (l *TestLogger) Warn(msg string, args ...interface{}) {
	l.logs = append(l.logs, LogEntry{
		Level:   "WARN",
		Message: msg,
		Args:    args,
		Time:    time.Now(),
	})
}

// FormatHTTPLog 格式化HTTP日志
func (l *TestLogger) FormatHTTPLog(param gin.LogFormatterParams) string {
	return ""
}

// GetLogs 获取日志记录
func (l *TestLogger) GetLogs() []LogEntry {
	return l.logs
}

// ClearLogs 清空日志记录
func (l *TestLogger) ClearLogs() {
	l.logs = l.logs[:0]
}

// TestPerformance 性能测试
func TestPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过性能测试")
	}
	
	testLogger := &TestLogger{}
	config := apidebug.DefaultConfig()
	config.ProjectRoot = "../"
	
	service, err := apidebug.NewService(config, testLogger)
	require.NoError(t, err, "创建服务失败")
	
	ctx := context.Background()
	
	// 测试文档生成性能
	start := time.Now()
	_, err = service.GetAPIDocService().GenerateDocumentation(ctx)
	duration := time.Since(start)
	
	require.NoError(t, err, "生成文档失败")
	t.Logf("文档生成耗时: %v", duration)
	
	// 性能应该在合理范围内（小于5秒）
	assert.Less(t, duration, 5*time.Second, "文档生成时间应该在5秒内")
}

// TestConcurrency 并发测试
func TestConcurrency(t *testing.T) {
	testLogger := &TestLogger{}
	config := apidebug.DefaultConfig()
	config.ProjectRoot = "../"
	
	service, err := apidebug.NewService(config, testLogger)
	require.NoError(t, err, "创建服务失败")
	
	ctx := context.Background()
	err = service.Initialize(ctx)
	require.NoError(t, err, "初始化服务失败")
	
	// 并发获取系统状态
	const numGoroutines = 10
	done := make(chan error, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func() {
			_, err := service.GetSystemStatus(ctx)
			done <- err
		}()
	}
	
	// 等待所有goroutine完成
	for i := 0; i < numGoroutines; i++ {
		err := <-done
		assert.NoError(t, err, "并发获取系统状态不应该失败")
	}
}
