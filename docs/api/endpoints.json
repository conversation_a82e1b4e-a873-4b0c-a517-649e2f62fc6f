[{"method": "GET", "path": "/health", "handler": "healthHandler", "summary": "", "description": "", "tags": ["Health"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/api/health", "handler": "healthHandler", "summary": "", "description": "", "tags": ["Api"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/:provider/url", "handler": "authHandler.GetAuthURL", "summary": "获取OAuth认证URL", "description": "获取指定提供商的OAuth认证URL", "tags": ["认证"], "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"auth_url": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/:provider/callback", "handler": "authHandler.HandleCallback", "summary": "处理OAuth回调", "description": "处理OAuth认证回调，完成用户登录", "tags": ["认证"], "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "code", "in": "query", "required": true, "description": "授权码", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "user": {"type": "object", "title": "models.User", "description": "对象类型: models.User"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": null}, {"method": "POST", "path": "/refresh", "handler": "authHandler.RefreshToken", "summary": "刷新JWT token", "description": "刷新即将过期的JWT token", "tags": ["认证"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/validation/config", "handler": "validationHandler.GetValidationConfig", "summary": "获取校验配置", "description": "获取当前的内容校验配置信息", "tags": ["Validation"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ValidationConfigResponse", "description": "数据对象: ValidationConfigResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/profile", "handler": "authHandler.GetProfile", "summary": "获取当前用户资料", "description": "获取当前认证用户的详细资料信息", "tags": ["认证"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/profile", "handler": "authHandler.UpdateProfile", "summary": "更新用户资料", "description": "更新当前用户的资料信息", "tags": ["认证"], "parameters": [{"name": "profile", "in": "body", "required": true, "description": "用户资料", "example": null, "schema": {"type": "object", "title": "UpdateProfileRequest", "description": "复杂对象类型: UpdateProfileRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/validate", "handler": "validationHandler.ValidateContent", "summary": "校验内容", "description": "对用户输入的内容进行校验和过滤", "tags": ["Validation"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "校验内容请求", "example": null, "schema": {"type": "object", "title": "ValidateContentRequest", "description": "复杂对象类型: ValidateContentRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "validation.ValidationResult", "description": "数据对象: validation.ValidationResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/batch-validate", "handler": "validationHandler.BatchValidateContent", "summary": "批量校验内容", "description": "批量校验多个内容项", "tags": ["Validation"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "校验内容请求", "example": null, "schema": {"type": "object", "title": "ValidateContentRequest", "description": "复杂对象类型: ValidateContentRequest"}}, {"name": "request", "in": "body", "required": true, "description": "批量校验内容请求", "example": null, "schema": {"type": "object", "title": "BatchValidateContentRequest", "description": "复杂对象类型: BatchValidateContentRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "BatchValidationResult", "description": "数据对象: BatchValidationResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/stats", "handler": "validationHandler.GetValidationStats", "summary": "获取校验统计信息", "description": "获取用户的内容校验统计信息", "tags": ["Validation"], "parameters": [{"name": "days", "in": "query", "required": false, "description": "统计天数", "example": "7", "schema": {"type": "integer", "format": "int32", "example": "7"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate", "handler": "aiHandler.GenerateContent", "summary": "生成AI内容", "description": "使用AI生成游戏内容，如场景、角色、事件等", "tags": ["AI"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/scene", "handler": "aiHandler.GenerateScene", "summary": "生成游戏场景", "description": "根据场景参数生成游戏场景描述和属性", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "场景生成请求", "example": null, "schema": {"type": "object", "title": "GenerateSceneRequest", "description": "复杂对象类型: GenerateSceneRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/character", "handler": "aiHandler.GenerateCharacter", "summary": "生成游戏角色", "description": "根据提示词生成游戏角色描述和属性", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "角色生成请求", "example": null, "schema": {"type": "object", "title": "GenerateCharacterRequest", "description": "复杂对象类型: GenerateCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/event", "handler": "aiHandler.GenerateEvent", "summary": "生成游戏事件", "description": "根据提示词生成游戏事件描述和效果", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "事件生成请求", "example": null, "schema": {"type": "object", "title": "GenerateEventRequest", "description": "复杂对象类型: GenerateEventRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/history", "handler": "aiHandler.GetInteractionHistory", "summary": "获取AI交互历史", "description": "获取用户或世界的AI交互历史记录", "tags": ["AI"], "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "limit", "in": "query", "required": false, "description": "限制数量", "example": "50", "schema": {"type": "integer", "format": "int32", "example": "50"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "[]models.AIInteraction", "description": "数据对象: []models.AIInteraction"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/stats", "handler": "aiHandler.GetTokenUsageStats", "summary": "获取token使用统计", "description": "获取用户或世界的token使用统计信息", "tags": ["AI"], "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "days", "in": "query", "required": false, "description": "统计天数", "example": "30", "schema": {"type": "integer", "format": "int32", "example": "30"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "object", "description": "数据对象: object"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate", "handler": "gameHandler.GenerateWorlds", "summary": "AI生成世界配置", "description": "使用AI生成多个候选世界配置供用户选择", "tags": ["Game"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "生成世界配置请求", "example": null, "schema": {"type": "object", "title": "GenerateWorldsRequest", "description": "复杂对象类型: GenerateWorldsRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "GenerateWorldsResponse", "description": "数据对象: GenerateWorldsResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id", "handler": "gameHandler.GetWorld", "summary": "获取世界信息", "description": "获取指定世界的详细信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.World", "description": "数据对象: models.World"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/:world_id", "handler": "gameHandler.UpdateWorld", "summary": "更新世界信息", "description": "更新指定世界的信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新世界请求", "example": null, "schema": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "DELETE", "path": "/:world_id", "handler": "gameHandler.DeleteWorld", "summary": "删除世界", "description": "删除指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/join", "handler": "gameHandler.JoinWorld", "summary": "加入世界", "description": "用户加入指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/leave", "handler": "gameHandler.LeaveWorld", "summary": "离开世界", "description": "用户离开指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/characters", "handler": "gameHandler.GetWorldCharacters", "summary": "获取世界中的角色列表", "description": "获取指定世界中的角色列表，支持按角色类型筛选", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}, {"name": "character_type", "in": "query", "required": false, "description": "角色类型", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/state", "handler": "gameHandler.GetWorldState", "summary": "获取世界状态", "description": "获取指定世界的当前状态信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "game.GameState", "description": "数据对象: game.GameState"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time", "handler": "gameHandler.UpdateWorldTime", "summary": "更新世界信息", "description": "更新指定世界的信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "时间更新请求", "example": null, "schema": {"type": "object", "title": "UpdateTimeRequest", "description": "复杂对象类型: UpdateTimeRequest"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新世界请求", "example": null, "schema": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/tick", "handler": "gameHandler.ProcessWorldTick", "summary": "处理世界时钟周期", "description": "执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/time-schedule", "handler": "timeScheduleHandler.GetTimeScheduleConfig", "summary": "获取世界的时间段配置", "description": "获取指定世界的时间段配置信息", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.TimeScheduleConfig", "description": "数据对象: models.TimeScheduleConfig"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/:world_id/time-schedule", "handler": "timeScheduleHandler.UpdateTimeScheduleConfig", "summary": "更新世界的时间段配置", "description": "更新指定世界的时间段配置，支持热更新", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/time-rate", "handler": "timeScheduleHandler.GetCurrentTimeRate", "summary": "获取世界当前的时间速率", "description": "获取指定世界当前生效的时间速率", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time-schedule/enable", "handler": "timeScheduleHandler.EnableTimeSchedule", "summary": "启用世界的时间段配置", "description": "启用指定世界的时间段配置功能", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time-schedule/disable", "handler": "timeScheduleHandler.DisableTimeSchedule", "summary": "禁用世界的时间段配置", "description": "禁用指定世界的时间段配置功能", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time-schedule/default", "handler": "timeScheduleHandler.CreateDefaultTimeSchedule", "summary": "为世界创建默认时间段配置", "description": "为指定世界创建默认的时间段配置", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time-schedule/reset", "handler": "timeScheduleHandler.ResetTimeScheduleConfig", "summary": "重置世界的时间段配置", "description": "将指定世界的时间段配置重置为默认值", "tags": ["TimeSchedule"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/my-worlds", "handler": "gameHandler.GetMyWorlds", "summary": "获取我的世界列表", "description": "获取当前用户创建的世界列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/public-worlds", "handler": "gameHandler.GetPublicWorlds", "summary": "获取公开世界列表", "description": "获取所有公开的世界列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/:character_id", "handler": "gameHandler.GetCharacter", "summary": "获取角色信息", "description": "获取指定角色的详细信息", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Character", "description": "数据对象: models.Character"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/:character_id", "handler": "gameHandler.UpdateCharacter", "summary": "更新角色信息", "description": "更新指定角色的基本信息", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新角色请求", "example": null, "schema": {"type": "object", "title": "UpdateCharacterRequest", "description": "复杂对象类型: UpdateCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "DELETE", "path": "/:character_id", "handler": "gameHandler.DeleteCharacter", "summary": "删除角色", "description": "删除指定的角色", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/move", "handler": "gameHandler.MoveCharacter", "summary": "移动角色", "description": "移动角色到指定场景", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "移动角色请求", "example": null, "schema": {"type": "object", "title": "MoveCharacterRequest", "description": "复杂对象类型: MoveCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/traits", "handler": "gameHandler.AddCharacterTrait", "summary": "添加角色特质", "description": "为指定角色添加新的特质", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加特质请求", "example": null, "schema": {"type": "object", "title": "AddTraitRequest", "description": "复杂对象类型: AddTraitRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/memories", "handler": "gameHandler.AddCharacterMemory", "summary": "添加角色记忆", "description": "为指定角色添加新的记忆", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加记忆请求", "example": null, "schema": {"type": "object", "title": "AddMemoryRequest", "description": "复杂对象类型: AddMemoryRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/experiences", "handler": "gameHandler.AddCharacterExperience", "summary": "添加角色阅历", "description": "为指定角色添加新的阅历", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加阅历请求", "example": null, "schema": {"type": "object", "title": "AddExperienceRequest", "description": "复杂对象类型: AddExperienceRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/actions", "handler": "gameHandler.PerformAction", "summary": "执行角色行动", "description": "角色执行指定的行动，如移动、交互、说话等", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "行动请求", "example": null, "schema": {"type": "object", "title": "PerformActionRequest", "description": "复杂对象类型: PerformActionRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ActionResult", "description": "数据对象: ActionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/interact/:target_character_id", "handler": "gameHandler.InteractWithCharacter", "summary": "与角色交互", "description": "角色与另一个角色进行交互", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "target_character_id", "in": "path", "required": true, "description": "目标角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "交互请求", "example": null, "schema": {"type": "object", "title": "InteractionRequest", "description": "复杂对象类型: InteractionRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "InteractionResult", "description": "数据对象: InteractionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/speak", "handler": "gameHandler.SpeakInScene", "summary": "在场景中说话", "description": "角色在当前场景中说话，其他角色可以听到", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "说话请求", "example": null, "schema": {"type": "object", "title": "SpeakRequest", "description": "复杂对象类型: SpeakRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "SpeechResult", "description": "数据对象: SpeechResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/my-characters", "handler": "gameHandler.GetMyCharacters", "summary": "获取我的角色列表", "description": "获取当前用户的角色列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:scene_id", "handler": "gameHandler.GetScene", "summary": "获取场景信息", "description": "获取指定场景的详细信息", "tags": ["Game"], "parameters": [{"name": "scene_id", "in": "path", "required": true, "description": "场景ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Scene", "description": "数据对象: models.Scene"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:event_id/process", "handler": "gameHandler.ProcessEvent", "summary": "处理事件", "description": "处理指定的事件", "tags": ["Game"], "parameters": [{"name": "event_id", "in": "path", "required": true, "description": "事件ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/trigger", "handler": "gameHandler.TriggerEvent", "summary": "触发事件", "description": "手动触发一个游戏事件", "tags": ["Game"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "触发事件请求", "example": null, "schema": {"type": "object", "title": "TriggerEventRequest", "description": "复杂对象类型: TriggerEventRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "EventResult", "description": "数据对象: EventResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/validate", "handler": "timeScheduleHandler.ValidateTimeScheduleConfig", "summary": "验证时间段配置", "description": "验证时间段配置的有效性，不保存到数据库", "tags": ["TimeSchedule"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/stats", "handler": "timeScheduleHandler.GetTimeScheduleStats", "summary": "获取时间段配置统计信息", "description": "获取系统中所有世界的时间段配置统计信息", "tags": ["TimeSchedule"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "map[string]interface{", "description": "数据对象: map[string]interface{"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:provider/url", "handler": "authHandler.GetAuthURL", "summary": "获取OAuth认证URL", "description": "获取指定提供商的OAuth认证URL", "tags": ["认证"], "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"auth_url": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/:provider/callback", "handler": "authHandler.HandleCallback", "summary": "处理OAuth回调", "description": "处理OAuth认证回调，完成用户登录", "tags": ["认证"], "parameters": [{"name": "provider", "in": "path", "required": true, "description": "认证提供商", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "code", "in": "query", "required": true, "description": "授权码", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "state", "in": "query", "required": false, "description": "状态参数", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "user": {"type": "object", "title": "models.User", "description": "对象类型: models.User"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": null}, {"method": "POST", "path": "/refresh", "handler": "authHandler.RefreshToken", "summary": "刷新JWT token", "description": "刷新即将过期的JWT token", "tags": ["认证"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "properties": {"expires_at": {"type": "string", "example": "示例文本", "description": "字符串类型"}, "token": {"type": "string", "example": "示例文本", "description": "字符串类型"}}}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/profile", "handler": "authHandler.GetProfile", "summary": "获取当前用户资料", "description": "获取当前认证用户的详细资料信息", "tags": ["认证"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/profile", "handler": "authHandler.UpdateProfile", "summary": "更新用户资料", "description": "更新当前用户的资料信息", "tags": ["认证"], "parameters": [{"name": "profile", "in": "body", "required": true, "description": "用户资料", "example": null, "schema": {"type": "object", "title": "UpdateProfileRequest", "description": "复杂对象类型: UpdateProfileRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.User", "description": "数据对象: models.User"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate", "handler": "aiHandler.GenerateContent", "summary": "生成AI内容", "description": "使用AI生成游戏内容，如场景、角色、事件等", "tags": ["AI"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/scene", "handler": "aiHandler.GenerateScene", "summary": "生成游戏场景", "description": "根据场景参数生成游戏场景描述和属性", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "场景生成请求", "example": null, "schema": {"type": "object", "title": "GenerateSceneRequest", "description": "复杂对象类型: GenerateSceneRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/character", "handler": "aiHandler.GenerateCharacter", "summary": "生成游戏角色", "description": "根据提示词生成游戏角色描述和属性", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "角色生成请求", "example": null, "schema": {"type": "object", "title": "GenerateCharacterRequest", "description": "复杂对象类型: GenerateCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate/event", "handler": "aiHandler.GenerateEvent", "summary": "生成游戏事件", "description": "根据提示词生成游戏事件描述和效果", "tags": ["AI"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "事件生成请求", "example": null, "schema": {"type": "object", "title": "GenerateEventRequest", "description": "复杂对象类型: GenerateEventRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ai.GenerateResponse", "description": "数据对象: ai.GenerateResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/history", "handler": "aiHandler.GetInteractionHistory", "summary": "获取AI交互历史", "description": "获取用户或世界的AI交互历史记录", "tags": ["AI"], "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "limit", "in": "query", "required": false, "description": "限制数量", "example": "50", "schema": {"type": "integer", "format": "int32", "example": "50"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "[]models.AIInteraction", "description": "数据对象: []models.AIInteraction"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/stats", "handler": "aiHandler.GetTokenUsageStats", "summary": "获取token使用统计", "description": "获取用户或世界的token使用统计信息", "tags": ["AI"], "parameters": [{"name": "world_id", "in": "query", "required": false, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "days", "in": "query", "required": false, "description": "统计天数", "example": "30", "schema": {"type": "integer", "format": "int32", "example": "30"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "object", "description": "数据对象: object"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/generate", "handler": "gameHandler.GenerateWorlds", "summary": "AI生成世界配置", "description": "使用AI生成多个候选世界配置供用户选择", "tags": ["Game"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "生成世界配置请求", "example": null, "schema": {"type": "object", "title": "GenerateWorldsRequest", "description": "复杂对象类型: GenerateWorldsRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "GenerateWorldsResponse", "description": "数据对象: GenerateWorldsResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id", "handler": "gameHandler.GetWorld", "summary": "获取世界信息", "description": "获取指定世界的详细信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.World", "description": "数据对象: models.World"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/:world_id", "handler": "gameHandler.UpdateWorld", "summary": "更新世界信息", "description": "更新指定世界的信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新世界请求", "example": null, "schema": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "DELETE", "path": "/:world_id", "handler": "gameHandler.DeleteWorld", "summary": "删除世界", "description": "删除指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/join", "handler": "gameHandler.JoinWorld", "summary": "加入世界", "description": "用户加入指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/leave", "handler": "gameHandler.LeaveWorld", "summary": "离开世界", "description": "用户离开指定的世界", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/characters", "handler": "gameHandler.GetWorldCharacters", "summary": "获取世界中的角色列表", "description": "获取指定世界中的角色列表，支持按角色类型筛选", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}, {"name": "character_type", "in": "query", "required": false, "description": "角色类型", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:world_id/state", "handler": "gameHandler.GetWorldState", "summary": "获取世界状态", "description": "获取指定世界的当前状态信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "game.GameState", "description": "数据对象: game.GameState"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/time", "handler": "gameHandler.UpdateWorldTime", "summary": "更新世界信息", "description": "更新指定世界的信息", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "时间更新请求", "example": null, "schema": {"type": "object", "title": "UpdateTimeRequest", "description": "复杂对象类型: UpdateTimeRequest"}}, {"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新世界请求", "example": null, "schema": {"type": "object", "title": "UpdateWorldRequest", "description": "复杂对象类型: UpdateWorldRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:world_id/tick", "handler": "gameHandler.ProcessWorldTick", "summary": "处理世界时钟周期", "description": "执行世界的一个时钟周期，包括时间推进、NPC行为、环境事件等", "tags": ["Game"], "parameters": [{"name": "world_id", "in": "path", "required": true, "description": "世界ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/my-worlds", "handler": "gameHandler.GetMyWorlds", "summary": "获取我的世界列表", "description": "获取当前用户创建的世界列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/public-worlds", "handler": "gameHandler.GetPublicWorlds", "summary": "获取公开世界列表", "description": "获取所有公开的世界列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/:character_id", "handler": "gameHandler.GetCharacter", "summary": "获取角色信息", "description": "获取指定角色的详细信息", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Character", "description": "数据对象: models.Character"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "PUT", "path": "/:character_id", "handler": "gameHandler.UpdateCharacter", "summary": "更新角色信息", "description": "更新指定角色的基本信息", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "更新角色请求", "example": null, "schema": {"type": "object", "title": "UpdateCharacterRequest", "description": "复杂对象类型: UpdateCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "DELETE", "path": "/:character_id", "handler": "gameHandler.DeleteCharacter", "summary": "删除角色", "description": "删除指定的角色", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/move", "handler": "gameHandler.MoveCharacter", "summary": "移动角色", "description": "移动角色到指定场景", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "移动角色请求", "example": null, "schema": {"type": "object", "title": "MoveCharacterRequest", "description": "复杂对象类型: MoveCharacterRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/traits", "handler": "gameHandler.AddCharacterTrait", "summary": "添加角色特质", "description": "为指定角色添加新的特质", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加特质请求", "example": null, "schema": {"type": "object", "title": "AddTraitRequest", "description": "复杂对象类型: AddTraitRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/memories", "handler": "gameHandler.AddCharacterMemory", "summary": "添加角色记忆", "description": "为指定角色添加新的记忆", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加记忆请求", "example": null, "schema": {"type": "object", "title": "AddMemoryRequest", "description": "复杂对象类型: AddMemoryRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/experiences", "handler": "gameHandler.AddCharacterExperience", "summary": "添加角色阅历", "description": "为指定角色添加新的阅历", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "添加阅历请求", "example": null, "schema": {"type": "object", "title": "AddExperienceRequest", "description": "复杂对象类型: AddExperienceRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/actions", "handler": "gameHandler.PerformAction", "summary": "执行角色行动", "description": "角色执行指定的行动，如移动、交互、说话等", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "行动请求", "example": null, "schema": {"type": "object", "title": "PerformActionRequest", "description": "复杂对象类型: PerformActionRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "ActionResult", "description": "数据对象: ActionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/interact/:target_character_id", "handler": "gameHandler.InteractWithCharacter", "summary": "与角色交互", "description": "角色与另一个角色进行交互", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "target_character_id", "in": "path", "required": true, "description": "目标角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "交互请求", "example": null, "schema": {"type": "object", "title": "InteractionRequest", "description": "复杂对象类型: InteractionRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "InteractionResult", "description": "数据对象: InteractionResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:character_id/speak", "handler": "gameHandler.SpeakInScene", "summary": "在场景中说话", "description": "角色在当前场景中说话，其他角色可以听到", "tags": ["Game"], "parameters": [{"name": "character_id", "in": "path", "required": true, "description": "角色ID", "example": null, "schema": {"type": "string", "example": "example"}}, {"name": "request", "in": "body", "required": true, "description": "说话请求", "example": null, "schema": {"type": "object", "title": "SpeakRequest", "description": "复杂对象类型: SpeakRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "SpeechResult", "description": "数据对象: SpeechResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "403": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/my-characters", "handler": "gameHandler.GetMyCharacters", "summary": "获取我的角色列表", "description": "获取当前用户的角色列表", "tags": ["Game"], "parameters": [{"name": "page", "in": "query", "required": false, "description": "页码", "example": "1", "schema": {"type": "integer", "format": "int32", "example": "1"}}, {"name": "limit", "in": "query", "required": false, "description": "每页数量", "example": "20", "schema": {"type": "integer", "format": "int32", "example": "20"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "PaginatedResponse", "description": "数据对象: PaginatedResponse"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/:scene_id", "handler": "gameHandler.GetScene", "summary": "获取场景信息", "description": "获取指定场景的详细信息", "tags": ["Game"], "parameters": [{"name": "scene_id", "in": "path", "required": true, "description": "场景ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "models.Scene", "description": "数据对象: models.Scene"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "404": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/:event_id/process", "handler": "gameHandler.ProcessEvent", "summary": "处理事件", "description": "处理指定的事件", "tags": ["Game"], "parameters": [{"name": "event_id", "in": "path", "required": true, "description": "事件ID", "example": null, "schema": {"type": "string", "example": "example"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "POST", "path": "/trigger", "handler": "gameHandler.TriggerEvent", "summary": "触发事件", "description": "手动触发一个游戏事件", "tags": ["Game"], "parameters": [{"name": "request", "in": "body", "required": true, "description": "触发事件请求", "example": null, "schema": {"type": "object", "title": "TriggerEventRequest", "description": "复杂对象类型: TriggerEventRequest"}}], "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"data": {"type": "object", "title": "EventResult", "description": "数据对象: EventResult"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "API响应对象: Response"}, "examples": null}, "400": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "401": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}, "500": {"description": "错误响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/health", "handler": "healthCheck", "summary": "", "description": "", "tags": ["Health"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/:id", "handler": "getUserByID", "summary": "", "description": "", "tags": [":Id"], "parameters": null, "responses": null, "security": null}, {"method": "PUT", "path": "/:id/preferences", "handler": "updateUserPreferences", "summary": "", "description": "", "tags": [":Id"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/search", "handler": "searchUsers", "summary": "", "description": "", "tags": ["Search"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/:id", "handler": "getWorldByID", "summary": "", "description": "", "tags": [":Id"], "parameters": null, "responses": null, "security": null}, {"method": "PUT", "path": "/:id/config", "handler": "updateWorldConfig", "summary": "", "description": "", "tags": [":Id"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/search", "handler": "searchWorlds", "summary": "", "description": "", "tags": ["Search"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/database/info", "handler": "getDatabaseInfo", "summary": "", "description": "", "tags": ["Database"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/health", "handler": "healthHandler", "summary": "", "description": "", "tags": ["Health"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/api/health", "handler": "healthHandler", "summary": "", "description": "", "tags": ["Api"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/user/profile", "handler": "getUserProfile", "summary": "", "description": "", "tags": ["User"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/auth/login", "handler": "mockLogin", "summary": "", "description": "", "tags": ["<PERSON><PERSON>"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/auth/logout", "handler": "mockLogout", "summary": "用户登出", "description": "用户登出（客户端需要删除本地token）", "tags": ["认证"], "parameters": null, "responses": {"200": {"description": "成功响应", "schema": {"type": "object", "title": "Response", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: Response"}, "examples": null}}, "security": [{"BearerAuth": []}]}, {"method": "GET", "path": "/worlds", "handler": "getWorlds", "summary": "", "description": "", "tags": ["Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/worlds", "handler": "createWorld", "summary": "创建世界", "description": "创建一个新的游戏世界", "tags": ["世界管理"], "parameters": [{"name": "world", "in": "body", "required": true, "description": "世界创建参数", "example": null, "schema": {"type": "object", "properties": {"description": {"type": "string", "example": "example", "description": "string类型的字段"}, "name": {"type": "string", "example": "example", "description": "string类型的字段"}, "settings": {"type": "object", "properties": {"difficulty": {"type": "string", "example": "example", "description": "string类型的字段"}, "max_players": {"type": "integer", "format": "int32", "example": 1, "description": "int类型的字段"}}, "description": "object{difficulty=string,max_players=int}类型的字段"}}}}], "responses": {"200": {"description": "创建成功", "schema": {"type": "object", "title": "APIResponse", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: APIResponse"}, "examples": null}}, "security": null}, {"method": "GET", "path": "/worlds/:id", "handler": "getWorld", "summary": "", "description": "", "tags": ["Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/my-worlds", "handler": "getMyWorlds", "summary": "", "description": "", "tags": ["My-Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/public-worlds", "handler": "getPublicWorlds", "summary": "", "description": "", "tags": ["Public-Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/worlds", "handler": "createWorld", "summary": "创建世界", "description": "创建一个新的游戏世界", "tags": ["世界管理"], "parameters": [{"name": "world", "in": "body", "required": true, "description": "世界创建参数", "example": null, "schema": {"type": "object", "properties": {"description": {"type": "string", "example": "example", "description": "string类型的字段"}, "name": {"type": "string", "example": "example", "description": "string类型的字段"}, "settings": {"type": "object", "properties": {"difficulty": {"type": "string", "example": "example", "description": "string类型的字段"}, "max_players": {"type": "integer", "format": "int32", "example": 1, "description": "int类型的字段"}}, "description": "object{difficulty=string,max_players=int}类型的字段"}}}}], "responses": {"200": {"description": "创建成功", "schema": {"type": "object", "title": "APIResponse", "properties": {"code": {"type": "integer", "example": 200, "description": "响应状态码"}, "data": {"type": "object", "example": {}, "description": "响应数据，具体结构根据接口而定"}, "message": {"type": "string", "example": "操作成功", "description": "响应消息"}, "success": {"type": "boolean", "example": true, "description": "操作是否成功"}, "timestamp": {"type": "integer", "format": "int64", "example": **********, "description": "时间戳"}}, "required": ["success", "message", "timestamp"], "description": "统一API响应格式: APIResponse"}, "examples": null}}, "security": null}, {"method": "PUT", "path": "/worlds/:id", "handler": "updateWorld", "summary": "", "description": "", "tags": ["Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "DELETE", "path": "/worlds/:id", "handler": "deleteWorld", "summary": "", "description": "", "tags": ["Worlds"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/my-characters", "handler": "getMyCharacters", "summary": "", "description": "", "tags": ["My-Characters"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/characters", "handler": "createCharacter", "summary": "", "description": "", "tags": ["Characters"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/characters/:id", "handler": "getCharacter", "summary": "", "description": "", "tags": ["Characters"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/world/:worldId/characters", "handler": "getWorldCharacters", "summary": "", "description": "", "tags": ["World"], "parameters": null, "responses": null, "security": null}, {"method": "GET", "path": "/games/:worldId/status", "handler": "getGameStatus", "summary": "", "description": "", "tags": ["Games"], "parameters": null, "responses": null, "security": null}, {"method": "POST", "path": "/games/:worldId/actions", "handler": "performAction", "summary": "", "description": "", "tags": ["Games"], "parameters": null, "responses": null, "security": null}]