# 测试文件移动记录
# 格式: 原路径 -> 新路径 (测试类型)

./internal/ai/game_schemas_test.go -> tests/unit/ai/ai_game_schemas_test.go (unit)
./internal/ai/integration_test.go -> tests/integration/ai/ai_integration_test.go (integration)
./internal/ai/schema_test.go -> tests/unit/ai/ai_schema_test.go (unit)
./internal/ai/service_test.go -> tests/integration/ai/ai_service_test.go (integration)
./internal/ai/transformer_test.go -> tests/unit/ai/ai_transformer_test.go (unit)
./internal/apidoc/scanner_test.go -> tests/integration/apidoc/apidoc_scanner_test.go (integration)
./internal/debug/service_test.go -> tests/integration/debug/debug_service_test.go (integration)
./internal/game/time_manager_test.go -> tests/integration/game/game_time_manager_test.go (integration)
./internal/game/world_service_enhanced_test.go -> tests/integration/game/game_world_service_enhanced_test.go (integration)
./internal/game/world_service_test.go -> tests/integration/game/game_world_service_test.go (integration)
./internal/handlers/ai_test.go -> tests/unit/handlers/handlers_ai_test.go (unit)
./internal/handlers/game_character_test.go -> tests/integration/handlers/handlers_game_character_test.go (integration)
./internal/handlers/game_test.go -> tests/integration/handlers/handlers_game_test.go (integration)
./internal/migration/smart_migrator_test.go -> tests/integration/migration/migration_smart_migrator_test.go (integration)
./internal/models/character_memory_test.go -> tests/unit/models/models_character_memory_test.go (unit)
./internal/models/time_schedule_test.go -> tests/unit/models/models_time_schedule_test.go (unit)
./internal/models/user_session_test.go -> tests/integration/models/models_user_session_test.go (integration)
./internal/models/v4_models_integration_test.go -> tests/integration/models/models_v4_models_integration_test.go (integration)
./internal/services/auth/auth_test.go -> tests/integration/services/services_auth_test.go (integration)
./internal/services/game/character_test.go -> tests/unit/services/services_character_test.go (unit)
./internal/services/game/world_test.go -> tests/unit/services/services_world_test.go (unit)
./internal/validation/service_test.go -> tests/integration/validation/validation_service_test.go (integration)
./pkg/database/compatibility_test.go -> tests/integration/pkg/pkg_compatibility_test.go (integration)
./test/apidebug_integration_test.go -> tests/integration/misc/misc_apidebug_integration_test.go (integration)
./test_simple_server_fixes_test.go -> tests/integration/misc/misc_test_simple_server_fixes_test.go (integration)
./test/world_generation_test.go -> tests/integration/misc/misc_world_generation_test.go (integration)
